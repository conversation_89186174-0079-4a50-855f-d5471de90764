<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4-in-a-Row Grammar Game - Restaurant Vocabulary</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4);
            background-size: 400% 400%;
            animation: gradientShift 10s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .instructions {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #856404;
        }

        .team-indicator {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .game-board {
            display: grid;
            grid-template-columns: auto repeat(7, 1fr);
            grid-template-rows: repeat(8, auto);
            gap: 5px;
            margin: 0 auto;
            max-width: 900px;
            border: 3px solid #333;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .header-cell {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            font-size: 18px;
            padding: 10px;
            text-align: center;
            border-radius: 5px;
        }

        .game-cell {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 12px;
            position: relative;
        }

        .game-cell:hover:not(.claimed) {
            transform: scale(1.05);
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .activity-word {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 11px;
        }

        .symbol {
            font-size: 20px;
            font-weight: bold;
        }

        .symbol.positive { color: #28a745; }
        .symbol.negative { color: #dc3545; }
        .symbol.question { color: #007bff; }

        .team-red {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
            border-color: #c82333;
        }

        .team-blue {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-color: #0056b3;
        }

        .claimed {
            opacity: 0.9;
        }

        .claimed .activity-word,
        .claimed .symbol {
            color: white !important;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-undo { 
            background: linear-gradient(45deg, #2196F3, #42A5F5); 
            color: white; 
        }
        
        .btn-reset { 
            background: linear-gradient(45deg, #f44336, #EF5350); 
            color: white; 
        }

        .win-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px 50px;
            border-radius: 20px;
            font-size: 32px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
            animation: celebrate 0.5s ease-in-out;
        }

        @keyframes celebrate {
            0% { transform: translate(-50%, -50%) scale(0.5); }
            100% { transform: translate(-50%, -50%) scale(1); }
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <a href="lesson_plan_main.html" class="back-btn">← Back to Lesson</a>
    
    <div class="container">
        <div class="header">
            <h1>🎯 4-in-a-Row Grammar Game</h1>
            <p><strong>Restaurant Vocabulary & Present Simple vs Present Continuous</strong></p>
            <div class="instructions">
                <strong>How to play:</strong> Teams say where (A1-G7) and what type (+/-/?).
                Teacher clicks the square. Get 4 in a row to win!
            </div>
        </div>

        <div class="legend">
            <div class="legend-item">
                <span class="symbol positive">+</span>
                <span>Positive (usually/often)</span>
            </div>
            <div class="legend-item">
                <span class="symbol negative">-</span>
                <span>Negative (don't/doesn't)</span>
            </div>
            <div class="legend-item">
                <span class="symbol question">?</span>
                <span>Question (Do/Does...? Are/Is...?)</span>
            </div>
        </div>
        
        <div class="team-indicator">
            Next team: <span id="nextTeam" style="color: #dc3545;">Red Team</span>
        </div>
        
        <div class="game-board" id="gameBoard">
            <!-- Grid will be generated here -->
        </div>
        
        <div class="controls">
            <a href="lesson_plan_main.html" class="back-btn">← Main Menu</a>
            <button class="btn btn-undo" id="undoBtn">↶ Undo Last Move</button>
            <button class="btn btn-reset" id="resetBtn">🔄 Reset Game</button>
        </div>
    </div>

    <div class="win-message" id="winMessage"></div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const activities = [
                'eat pasta',
                'drink coffee',
                'wear uniforms',
                'serve food',
                'order pizza',
                'have ice cream',
                'sit at tables',
                'read menus'
            ];

            const symbols = ['+', '-', '?'];
            const gameBoard = document.getElementById('gameBoard');
            const undoBtn = document.getElementById('undoBtn');
            const resetBtn = document.getElementById('resetBtn');
            const winMessage = document.getElementById('winMessage');
            const nextTeamDisplay = document.getElementById('nextTeam');

            let board = Array(7).fill(null).map(() => Array(7).fill(null));
            let moveHistory = [];
            let currentTeam = 'red'; // alternates between 'red' and 'blue'
            let gameData = []; // stores the activity and symbol for each cell

            function initializeGame() {
                generateGameData();
                createGameBoard();
                updateTeamDisplay();
            }

            function generateGameData() {
                // Create array of all possible combinations
                let combinations = [];
                activities.forEach(activity => {
                    symbols.forEach(symbol => {
                        combinations.push({ activity, symbol });
                    });
                });

                // Fill 7x7 grid with random combinations (some may repeat)
                gameData = Array(7).fill(null).map(() => Array(7).fill(null));
                for (let r = 0; r < 7; r++) {
                    for (let c = 0; c < 7; c++) {
                        const randomIndex = Math.floor(Math.random() * combinations.length);
                        gameData[r][c] = combinations[randomIndex];
                    }
                }
            }

            function createGameBoard() {
                gameBoard.innerHTML = '';
                
                // Empty corner cell
                gameBoard.appendChild(createHeaderCell(''));
                
                // Column headers (A-G)
                for (let i = 0; i < 7; i++) {
                    gameBoard.appendChild(createHeaderCell(String.fromCharCode(65 + i)));
                }
                
                // Rows with row headers and game cells
                for (let r = 0; r < 7; r++) {
                    // Row header
                    gameBoard.appendChild(createHeaderCell(r + 1));
                    
                    // Game cells
                    for (let c = 0; c < 7; c++) {
                        const cell = createGameCell(r, c);
                        gameBoard.appendChild(cell);
                    }
                }
            }

            function createHeaderCell(text) {
                const cell = document.createElement('div');
                cell.className = 'header-cell';
                cell.textContent = text;
                return cell;
            }

            function createGameCell(row, col) {
                const cell = document.createElement('div');
                cell.className = 'game-cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                const data = gameData[row][col];
                
                const activityDiv = document.createElement('div');
                activityDiv.className = 'activity-word';
                activityDiv.textContent = data.activity;
                
                const symbolDiv = document.createElement('div');
                symbolDiv.className = `symbol ${data.symbol === '+' ? 'positive' : data.symbol === '-' ? 'negative' : 'question'}`;
                symbolDiv.textContent = data.symbol;
                
                cell.appendChild(activityDiv);
                cell.appendChild(symbolDiv);
                
                cell.addEventListener('click', () => handleCellClick(row, col));
                
                return cell;
            }

            function handleCellClick(row, col) {
                if (board[row][col]) return; // Cell already claimed
                
                // Save move for undo
                moveHistory.push({ 
                    row, 
                    col, 
                    team: currentTeam
                });

                // Claim the cell for current team
                board[row][col] = { team: currentTeam };
                updateBoard();
                
                // Check for win
                if (checkWin(row, col)) {
                    const teamName = currentTeam === 'red' ? 'Red Team' : 'Blue Team';
                    winMessage.textContent = `🎉 ${teamName} Wins! 🎉`;
                    winMessage.style.display = 'block';
                    // Disable further clicks
                    gameBoard.style.pointerEvents = 'none';
                    return;
                }
                
                // Switch teams
                currentTeam = currentTeam === 'red' ? 'blue' : 'red';
                updateTeamDisplay();
            }

            function updateBoard() {
                for (let r = 0; r < 7; r++) {
                    for (let c = 0; c < 7; c++) {
                        const cell = document.querySelector(`[data-row='${r}'][data-col='${c}']`);
                        if (!cell) continue;
                        
                        // Remove previous team classes
                        cell.classList.remove('team-red', 'team-blue', 'claimed');
                        
                        if (board[r][c]) {
                            cell.classList.add(`team-${board[r][c].team}`, 'claimed');
                        }
                    }
                }
            }

            function updateTeamDisplay() {
                const teamName = currentTeam === 'red' ? 'Red Team' : 'Blue Team';
                nextTeamDisplay.textContent = teamName;
                nextTeamDisplay.style.color = currentTeam === 'red' ? '#dc3545' : '#007bff';
            }

            function undoMove() {
                if (moveHistory.length === 0) return;
                
                const lastMove = moveHistory.pop();
                board[lastMove.row][lastMove.col] = null;
                currentTeam = lastMove.team; // Reset to the team that made the last move
                
                winMessage.style.display = 'none';
                gameBoard.style.pointerEvents = 'auto';
                updateBoard();
                updateTeamDisplay();
            }

            function resetGame() {
                if (confirm('Are you sure you want to reset the game?')) {
                    board = Array(7).fill(null).map(() => Array(7).fill(null));
                    moveHistory = [];
                    currentTeam = 'red';
                    winMessage.style.display = 'none';
                    gameBoard.style.pointerEvents = 'auto';
                    
                    // Generate new random layout
                    generateGameData();
                    createGameBoard();
                    updateTeamDisplay();
                }
            }

            function checkWin(row, col) {
                const team = board[row][col] ? board[row][col].team : null;
                if (!team) return false;

                // Check all four directions: horizontal, vertical, diagonal \, diagonal /
                return checkDirection(row, col, 1, 0) ||  // Horizontal
                       checkDirection(row, col, 0, 1) ||  // Vertical
                       checkDirection(row, col, 1, 1) ||  // Diagonal \
                       checkDirection(row, col, 1, -1);   // Diagonal /
            }
            
            function checkDirection(row, col, dRow, dCol) {
                const team = board[row][col].team;
                let count = 1;
                
                // Check positive direction
                for (let i = 1; i < 4; i++) {
                    const r = row + i * dRow;
                    const c = col + i * dCol;
                    if (r >= 0 && r < 7 && c >= 0 && c < 7 && 
                        board[r][c] && board[r][c].team === team) {
                        count++;
                    } else {
                        break;
                    }
                }
                
                // Check negative direction
                for (let i = 1; i < 4; i++) {
                    const r = row - i * dRow;
                    const c = col - i * dCol;
                    if (r >= 0 && r < 7 && c >= 0 && c < 7 && 
                        board[r][c] && board[r][c].team === team) {
                        count++;
                    } else {
                        break;
                    }
                }
                
                return count >= 4;
            }

            // Event Listeners
            undoBtn.addEventListener('click', undoMove);
            resetBtn.addEventListener('click', resetGame);

            // Initialize the game
            initializeGame();
        });
    </script>
</body>
</html> 