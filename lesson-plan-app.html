<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unit 1: The Restaurant - Interactive Lesson Plan</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            min-height: 80vh;
        }

        .page.active {
            display: block;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .header h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 20px;
        }

        .nav-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .nav-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .back-btn {
            background: #f56565;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #e53e3e;
            transform: translateY(-1px);
        }

        .game-grid {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }

        .card {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .card:hover {
            background: #edf2f7;
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .card.selected {
            background: #667eea;
            color: white;
            border-color: #5a67d8;
        }

        .timer {
            background: #4299e1;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin: 10px 0;
        }

        .score {
            background: #38a169;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin: 10px;
        }

        .puzzle-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .puzzle-card {
            background: #fff;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .puzzle-question {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: #2d3748;
        }

        .reveal-btn {
            background: #f6ad55;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .reveal-btn:hover {
            background: #ed8936;
        }

        .answer {
            background: #c6f6d5;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-weight: bold;
            color: #22543d;
            display: none;
        }

        .memory-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            max-width: 500px;
            margin: 20px auto;
        }

        .memory-cell {
            background: #4299e1;
            color: white;
            border: none;
            aspect-ratio: 1;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .memory-cell:hover {
            background: #3182ce;
            transform: scale(1.05);
        }

        .memory-cell.flipped {
            background: white;
            color: #2d3748;
            border: 2px solid #4299e1;
        }

        .memory-cell.matched {
            background: #38a169;
            color: white;
        }

        .memory-numbers {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
            max-width: 500px;
            margin: 10px auto;
            text-align: center;
        }

        .memory-number {
            font-weight: bold;
            color: #4a5568;
            padding: 5px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .control-btn {
            background: #38a169;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #2f855a;
            transform: translateY(-1px);
        }

        .quiz-question {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .quiz-option {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #667eea;
            background: #f0f7ff;
        }

        .quiz-option.correct {
            background: #c6f6d5;
            border-color: #38a169;
        }

        .quiz-option.incorrect {
            background: #fed7d7;
            border-color: #f56565;
        }

        .phase-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .activity-list {
            display: grid;
            gap: 10px;
            margin: 20px 0;
        }

        .activity-item {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .pronunciation-grid-container {
            max-width: 600px;
            margin: 20px auto;
        }

        .pronunciation-numbers {
            display: grid;
            grid-template-columns: 50px repeat(6, 1fr);
            gap: 5px;
            margin-bottom: 10px;
            text-align: center;
        }

        .pronunciation-grid {
            display: grid;
            grid-template-columns: 50px repeat(6, 1fr);
            gap: 5px;
        }

        .pronunciation-cell {
            background: #4299e1;
            color: white;
            border: none;
            aspect-ratio: 1;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 5px;
            text-align: center;
        }

        .coordinate-label {
            font-size: 0.7em;
            font-weight: normal;
            opacity: 0.8;
            margin-bottom: 2px;
        }

        .word-label {
            font-size: 0.85em;
            font-weight: bold;
        }

        .pronunciation-cell:hover {
            background: #3182ce;
            transform: scale(1.05);
        }

        .pronunciation-cell.bomb {
            background: #f56565 !important;
            animation: bomb-shake 0.5s;
        }

        .pronunciation-cell.safe {
            background: #38a169;
        }

        .pronunciation-cell.revealed {
            background: #edf2f7;
            color: #2d3748;
            cursor: default;
        }

        .pronunciation-row-label {
            background: #2d3748;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            border-radius: 8px;
        }

        @keyframes bomb-shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .tic-tac-toe-container {
            max-width: 700px;
            margin: 20px auto;
        }

        .tic-tac-toe-numbers {
            display: grid;
            grid-template-columns: 50px repeat(7, 1fr);
            gap: 5px;
            margin-bottom: 10px;
            text-align: center;
        }

        .tic-tac-toe-grid {
            display: grid;
            grid-template-columns: 50px repeat(7, 1fr);
            gap: 5px;
        }

        .tic-tac-toe-cell {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            aspect-ratio: 1;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 5px;
            text-align: center;
        }

        .tic-tac-toe-cell:hover {
            border-color: #667eea;
            background: #f0f7ff;
        }

        .tic-tac-toe-cell.red {
            background: #fed7d7;
            border-color: #f56565;
            color: #c53030;
        }

        .tic-tac-toe-cell.blue {
            background: #bee3f8;
            border-color: #4299e1;
            color: #2b6cb0;
        }

        .tic-tac-toe-row-label {
            background: #2d3748;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            border-radius: 8px;
        }

        .jeopardy-board {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 20px 0;
        }

        .jeopardy-category {
            background: #2d3748;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }

        .jeopardy-cell {
            background: #4299e1;
            color: white;
            border: none;
            padding: 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .jeopardy-cell:hover {
            background: #3182ce;
            transform: scale(1.02);
        }

        .jeopardy-cell.used {
            background: #a0aec0;
            cursor: default;
            transform: none;
        }

        .family-feud-board {
            display: grid;
            gap: 10px;
            margin: 20px 0;
        }

        .feud-answer {
            background: #2d3748;
            color: white;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .feud-answer:hover {
            background: #4a5568;
        }

        .feud-answer.revealed {
            background: #38a169;
        }

        .character-icon {
            margin: 20px 0;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gap {
            background: #f7fafc;
            border: 2px dashed #cbd5e0;
            border-radius: 4px;
            padding: 2px 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .gap:hover {
            border-color: #667eea;
            background: #f0f7ff;
        }

        .game-notification {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #2d3748;
            color: white;
            padding: 20px 40px;
            border-radius: 15px;
            font-size: 1.3em;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .game-notification.show {
            opacity: 1;
        }

        .game-notification.bomb {
            background: #f56565;
            animation: shake 0.5s ease-in-out;
        }

        .game-notification.elimination {
            background: #e53e3e;
        }

        .game-notification.winner {
            background: #38a169;
            animation: celebrate 0.6s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
            25% { transform: translate(-50%, -50%) rotate(-2deg); }
            75% { transform: translate(-50%, -50%) rotate(2deg); }
        }

        @keyframes celebrate {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Main Menu Page -->
        <div class="page active" id="main-menu">
            <div class="header">
                <h1>🍽️ Unit 1: The Restaurant</h1>
                <h2>Interactive Lesson Plan (60 minutes)</h2>
                <p><strong>Level:</strong> Young ESL Learners | <strong>Goal:</strong> Learn restaurant words and grammar</p>
            </div>

            <div class="phase-header">
                <h3>🧩 Phase 1 (30 mins): Engage – Study – Apply</h3>
                <p>Fun activities with Restaurant Puzzles & Word Games</p>
            </div>

            <div class="nav-menu">
                <button class="nav-btn" onclick="showPage('detective-puzzles')">
                    🍕 Restaurant Fun Puzzles
                </button>
                <button class="nav-btn" onclick="showPage('memory-game')">
                    🧠 Memory Game: Vocabulary
                </button>
                <button class="nav-btn" onclick="showPage('vocab-quiz')">
                    🔍 Vocabulary Quiz
                </button>
            </div>

            <div class="phase-header">
                <h3>🍽️ Phase 2 (30 mins): Engage – Study – Apply</h3>
                <p>Reading and fun games</p>
            </div>

            <div class="nav-menu">
                <button class="nav-btn" onclick="showPage('speed-reading')">
                    📚 Speed Reading: Restaurant Story
                </button>
                <button class="nav-btn" onclick="showPage('comprehension')">
                    ❓ Story Questions
                </button>
                <button class="nav-btn" onclick="showPage('gap-fill')">
                    📝 Fill the Gaps
                </button>
                <button class="nav-btn" onclick="showPage('reorder-text')">
                    🔄 Put in Order
                </button>
                <button class="nav-btn" onclick="showPage('pronunciation')">
                    🗣 Word Game
                </button>
                <button class="nav-btn" onclick="showPage('guess-who')">
                    🎭 Guess the Character
                </button>
                <button class="nav-btn" onclick="showPage('tic-tac-toe')">
                    ⭕ Tic Tac Toe (4 in a row)
                </button>
            </div>

            <div class="phase-header">
                <h3>🎉 Bonus Games</h3>
            </div>

            <div class="nav-menu">
                <button class="nav-btn" onclick="showPage('jeopardy')">
                    🏆 Jeopardy
                </button>
                <button class="nav-btn" onclick="showPage('family-feud')">
                    👨‍👩‍👧‍👦 Family Feud
                </button>
                <button class="nav-btn" onclick="showPage('writing-homework')">
                    ✍️ Writing Activity
                </button>
            </div>
        </div>

        <!-- Restaurant Fun Puzzles Page -->
        <div class="page" id="detective-puzzles">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>🍕 Restaurant Fun Puzzles</h2>
                <p>Fun puzzles about restaurants and food!</p>
            </div>

            <div class="puzzle-container">
                <div class="puzzle-card">
                    <div class="puzzle-question">
                        🍕 If a pizza has 8 slices and you eat 2, how many are left?
                    </div>
                    <button class="reveal-btn" onclick="showAnswer(this, '6 slices')">Show Answer</button>
                    <div class="answer"></div>
                </div>

                <div class="puzzle-card">
                    <div class="puzzle-question">
                        🥤 What can you drink but never eat?
                    </div>
                    <button class="reveal-btn" onclick="showAnswer(this, 'Water, juice, milk')">Show Answer</button>
                    <div class="answer"></div>
                </div>

                <div class="puzzle-card">
                    <div class="puzzle-question">
                        🍽️ What do you use to eat but never eat it?
                    </div>
                    <button class="reveal-btn" onclick="showAnswer(this, 'A fork or spoon')">Show Answer</button>
                    <div class="answer"></div>
                </div>

                <div class="puzzle-card">
                    <div class="puzzle-question">
                        👨‍🍳 Who cooks food but doesn't eat it at work?
                    </div>
                    <button class="reveal-btn" onclick="showAnswer(this, 'A chef')">Show Answer</button>
                    <div class="answer"></div>
                </div>

                <div class="puzzle-card">
                    <div class="puzzle-question">
                        🍦 I'm cold and sweet, kids love to eat me. What am I?
                    </div>
                    <button class="reveal-btn" onclick="showAnswer(this, 'Ice cream')">Show Answer</button>
                    <div class="answer"></div>
                </div>

                <div class="puzzle-card">
                    <div class="puzzle-question">
                        📋 What has food names but you can't eat it?
                    </div>
                    <button class="reveal-btn" onclick="showAnswer(this, 'A menu')">Show Answer</button>
                    <div class="answer"></div>
                </div>
            </div>
        </div>

        <!-- Memory Game Page -->
        <div class="page" id="memory-game">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>🧠 Memory Game: Vocabulary Matching</h2>
                <p>Match correct spelling with scrambled words | Grid A–D, 1–4</p>
            </div>

            <div class="controls">
                <button class="control-btn" onclick="shuffleMemoryGame()">🔀 Shuffle</button>
                <button class="control-btn" onclick="resetMemoryGame()">🔄 Reset</button>
                <div class="score" id="memory-score">Matches: 0/8</div>
            </div>

            <div class="memory-numbers">
                <div class="memory-number">1</div>
                <div class="memory-number">2</div>
                <div class="memory-number">3</div>
                <div class="memory-number">4</div>
            </div>

            <div class="memory-grid" id="memory-grid">
                <!-- Memory cards will be populated by JavaScript -->
            </div>

            <div class="memory-numbers">
                <div class="memory-number">A</div>
                <div class="memory-number">B</div>
                <div class="memory-number">C</div>
                <div class="memory-number">D</div>
            </div>
        </div>

        <!-- Vocabulary Detective Quiz Page -->
        <div class="page" id="vocab-quiz">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>🔍 Vocabulary Detective Quiz</h2>
                <p>Multiple choice by clicking cards</p>
            </div>

            <div class="controls">
                <div class="score" id="quiz-score">Score: 0/7</div>
                <button class="control-btn" onclick="resetVocabQuiz()">🔄 Reset Quiz</button>
            </div>

            <div id="vocab-questions">
                <!-- Questions will be populated by JavaScript -->
            </div>
        </div>

        <!-- Speed Reading Page -->
        <div class="page" id="speed-reading">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>📚 Speed Reading: Character Texts</h2>
                <p>Short profiles with highlighting (90–350 wpm)</p>
            </div>

            <div class="controls">
                <label for="reading-speed">Reading Speed: </label>
                <select id="reading-speed" onchange="updateReadingSpeed()">
                    <option value="90">90 WPM (Slow)</option>
                    <option value="150" selected>150 WPM (Normal)</option>
                    <option value="200">200 WPM (Fast)</option>
                    <option value="300">300 WPM (Very Fast)</option>
                    <option value="350">350 WPM (Speed Reading)</option>
                </select>
                
                <button class="control-btn" onclick="startReading()">▶️ Start Reading</button>
                <button class="control-btn" onclick="pauseReading()">⏸️ Pause</button>
                <button class="control-btn" onclick="resetReading()">🔄 Reset</button>
            </div>

            <div class="card" style="margin: 20px 0;">
                <h3>Select Character Profile:</h3>
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectCharacter('shaggy')">Norville "Shaggy" Rogers</div>
                    <div class="quiz-option" onclick="selectCharacter('velma')">Velma Dinkley</div>
                    <div class="quiz-option" onclick="selectCharacter('daphne')">Daphne Blake</div>
                    <div class="quiz-option" onclick="selectCharacter('fred')">Fred Jones</div>
                </div>
            </div>

            <div class="card" id="reading-text" style="min-height: 300px; font-size: 1.2em; line-height: 1.6;">
                <p>Select a character profile to begin speed reading.</p>
            </div>
        </div>

        <!-- Comprehension Quiz Page -->
        <div class="page" id="comprehension">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>❓ Comprehension Quiz</h2>
                <p>Answer questions about the character profiles</p>
            </div>

            <div class="controls">
                <div class="score" id="comprehension-score">Score: 0/6</div>
                <button class="control-btn" onclick="resetComprehensionQuiz()">🔄 Reset Quiz</button>
            </div>

            <div id="comprehension-questions">
                <!-- Questions will be populated by JavaScript -->
            </div>
        </div>

        <!-- Gap Fill Exercise Page -->
        <div class="page" id="gap-fill">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>📝 Gap Fill Exercise</h2>
                <p>Click missing words to drop them in place</p>
            </div>

            <div class="controls">
                <div class="score" id="gap-fill-score">Completed: 0/8</div>
                <button class="control-btn" onclick="resetGapFill()">🔄 Reset</button>
            </div>

            <div class="card">
                <h3>Available Words:</h3>
                <div class="quiz-options" id="word-bank">
                    <!-- Words will be populated by JavaScript -->
                </div>
            </div>

            <div class="card" id="gap-fill-text">
                <!-- Text with gaps will be populated by JavaScript -->
            </div>
        </div>

        <!-- Reorder Text Page -->
        <div class="page" id="reorder-text">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>🔄 Reorder Text Exercise</h2>
                <p>Drag sentences to the right order</p>
            </div>

            <div class="controls">
                <button class="control-btn" onclick="shuffleReorderText()">🔀 Shuffle</button>
                <button class="control-btn" onclick="checkReorderText()">✅ Check Order</button>
                <button class="control-btn" onclick="resetReorderText()">🔄 Reset</button>
                <div class="score" id="reorder-score">Status: Not Started</div>
            </div>

            <div id="reorder-container">
                <!-- Sentences will be populated by JavaScript -->
            </div>
        </div>

        <!-- Pronunciation Minefield Page -->
        <div class="page" id="pronunciation">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>🗣 Minefield Pronunciation Game</h2>
                <p>Grid A–F, 1–6; 36 words | 4 teams, 3 lives, 10 bombs hidden | Last team standing wins!</p>
            </div>

            <div class="controls">
                <div class="score">Team 1: <span id="team1-lives">❤️❤️❤️</span></div>
                <div class="score">Team 2: <span id="team2-lives">❤️❤️❤️</span></div>
                <div class="score">Team 3: <span id="team3-lives">❤️❤️❤️</span></div>
                <div class="score">Team 4: <span id="team4-lives">❤️❤️❤️</span></div>
                <button class="control-btn" onclick="resetPronunciation()">🔄 Reset</button>
                <div class="score">Current Team: <span id="current-team">Team 1</span></div>
            </div>

            <div class="pronunciation-grid-container">
                <div class="pronunciation-numbers">
                    <div></div>
                    <div class="memory-number">1</div>
                    <div class="memory-number">2</div>
                    <div class="memory-number">3</div>
                    <div class="memory-number">4</div>
                    <div class="memory-number">5</div>
                    <div class="memory-number">6</div>
                </div>
                <div class="pronunciation-grid" id="pronunciation-grid">
                    <!-- Grid will be populated by JavaScript -->
                </div>
            </div>

            <!-- Game notification popup -->
            <div class="game-notification" id="game-notification"></div>
        </div>

        <!-- Guess Who Page -->
        <div class="page" id="guess-who">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>🎭 Guess Who Detective Game</h2>
                <p>Character cards face down; teams flip cards, read clue aloud, guess who</p>
            </div>

            <div class="controls">
                <div class="score">Round: <span id="guess-who-round">1</span>/5</div>
                <div class="score">Score: <span id="guess-who-score">0</span></div>
                <button class="control-btn" onclick="nextGuessWhoRound()">Next Round</button>
                <button class="control-btn" onclick="resetGuessWho()">🔄 Reset</button>
            </div>

            <div class="guess-who-container">
                <div class="card" id="guess-who-card" style="min-height: 300px; text-align: center;">
                    <h3>Click the card to reveal the clue!</h3>
                    <div class="character-icon" id="character-display">
                        <!-- SVG character will be displayed here -->
                    </div>
                    <div id="character-clue" style="font-size: 1.2em; margin: 20px 0;">
                        Click the card above to start
                    </div>
                    <button class="control-btn" id="reveal-answer" onclick="revealGuessWhoAnswer()" style="display: none;">
                        Show Answer
                    </button>
                </div>
            </div>
        </div>

        <!-- Tic Tac Toe Page -->
        <div class="page" id="tic-tac-toe">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>⭕ Tic Tac Toe (4 in a row)</h2>
                <p>Grid A–G, 1–7 | Teams say correct sentence to claim square</p>
            </div>

            <div class="controls">
                <div class="score">🔴 Red Team: <span id="red-score">0</span></div>
                <div class="score">🔵 Blue Team: <span id="blue-score">0</span></div>
                <button class="control-btn" onclick="resetTicTacToe()">🔄 Reset</button>
                <div class="score">Current Turn: <span id="current-turn">🔴 Red</span></div>
            </div>

            <div class="tic-tac-toe-container">
                <div class="tic-tac-toe-numbers">
                    <div></div>
                    <div class="memory-number">1</div>
                    <div class="memory-number">2</div>
                    <div class="memory-number">3</div>
                    <div class="memory-number">4</div>
                    <div class="memory-number">5</div>
                    <div class="memory-number">6</div>
                    <div class="memory-number">7</div>
                </div>
                <div class="tic-tac-toe-grid" id="tic-tac-toe-grid">
                    <!-- Grid will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Jeopardy Page -->
        <div class="page" id="jeopardy">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>🏆 Jeopardy Game</h2>
                <p>Topics: restaurant words, food, story, grammar</p>
            </div>

            <div class="controls">
                <div class="score">Total Score: <span id="jeopardy-score">0</span></div>
                <button class="control-btn" onclick="resetJeopardy()">🔄 Reset</button>
            </div>

            <div class="jeopardy-board" id="jeopardy-board">
                <!-- Jeopardy board will be populated by JavaScript -->
            </div>
        </div>

        <!-- Family Feud Page -->
        <div class="page" id="family-feud">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>👨‍👩‍👧‍👦 Family Feud</h2>
                <p>Categories: Restaurant things, food, drinks, people</p>
            </div>

            <div class="controls">
                <div class="score">Round: <span id="feud-round">1</span>/4</div>
                <div class="score">Revealed: <span id="feud-revealed">0</span>/5</div>
                <button class="control-btn" onclick="nextFeudRound()">Next Category</button>
                <button class="control-btn" onclick="resetFamilyFeud()">🔄 Reset</button>
            </div>

            <div class="family-feud-container">
                <div class="card">
                    <h3 id="feud-category">Category: Things in a Restaurant</h3>
                    <div class="family-feud-board" id="family-feud-board">
                        <!-- Answers will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Writing Homework Page -->
        <div class="page" id="writing-homework">
            <button class="back-btn" onclick="showPage('main-menu')">← Back to Menu</button>
            <div class="header">
                <h2>✍️ Writing Homework Guide</h2>
                <p>"My favorite character" - Writing to be done at home</p>
            </div>

            <div class="activity-list">
                <div class="activity-item">
                    <h3>📝 Paragraph 1: Who & Why</h3>
                    <p>Introduce your favorite character and explain why you chose them.</p>
                    <ul>
                        <li>Start with: "My favorite character is..."</li>
                        <li>Explain why: "I like him/her because..."</li>
                        <li>Use: <strong>because, I think, in my opinion</strong></li>
                    </ul>
                </div>

                <div class="activity-item">
                    <h3>🎭 Paragraph 2: 2-3 Traits</h3>
                    <p>Describe 2-3 character traits with examples.</p>
                    <ul>
                        <li>Choose traits: brave, funny, danger-prone, smart, friendly</li>
                        <li>Give examples from the stories</li>
                        <li>Use: <strong>and, but, also, he/she is/does</strong></li>
                    </ul>
                </div>

                <div class="activity-item">
                    <h3>🏁 Paragraph 3: Conclusion</h3>
                    <p>Wrap up your opinion.</p>
                    <ul>
                        <li>Summarize: "I think he/she is the best because..."</li>
                        <li>Final thought about the character</li>
                        <li>Use: <strong>in conclusion, finally, that's why</strong></li>
                    </ul>
                </div>

                <div class="activity-item">
                    <h3>🔤 Useful Phrases</h3>
                    <div class="quiz-options" style="grid-template-columns: repeat(4, 1fr);">
                        <div class="quiz-option">because</div>
                        <div class="quiz-option">and</div>
                        <div class="quiz-option">but</div>
                        <div class="quiz-option">also</div>
                        <div class="quiz-option">in my opinion</div>
                        <div class="quiz-option">I think</div>
                        <div class="quiz-option">he/she is</div>
                        <div class="quiz-option">he/she does</div>
                    </div>
                </div>

                <div class="activity-item">
                    <h3>📖 Sample Answer</h3>
                    <div class="card" style="background: #f0f7ff; border-left: 4px solid #4299e1;">
                        <p><strong>My favorite character is Velma</strong> because she is very smart and always solves the mysteries. I think she is the most important member of the team.</p>
                        
                        <p><strong>Velma is intelligent and brave.</strong> She wears glasses and she can't see without them. She also loves computers and she finds clues that help solve mysteries. But sometimes she loses her glasses and gets scared.</p>
                        
                        <p><strong>In conclusion, I think Velma is the best character</strong> because she uses her brain to help her friends. That's why she is my favorite character in Scooby-Doo.</p>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        // Global variables
        let currentPage = 'main-menu';
        let memoryCards = [];
        let flippedCards = [];
        let matchedPairs = 0;
        let vocabQuizScore = 0;
        let comprehensionScore = 0;
        let gapFillScore = 0;
        let readingInterval = null;
        let currentWordIndex = 0;
        let readingSpeed = 150;

        // Game notification system
        function showGameNotification(message, type = 'default', duration = 2000) {
            const notification = document.getElementById('game-notification');
            if (!notification) return;
            
            notification.textContent = message;
            notification.className = `game-notification ${type}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }

        // Navigation function
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // Show selected page
            document.getElementById(pageId).classList.add('active');
            currentPage = pageId;

            // Initialize page-specific content
            if (pageId === 'memory-game') {
                initMemoryGame();
            } else if (pageId === 'vocab-quiz') {
                initVocabQuiz();
            } else if (pageId === 'comprehension') {
                initComprehensionQuiz();
            } else if (pageId === 'gap-fill') {
                initGapFill();
            } else if (pageId === 'reorder-text') {
                initReorderText();
            } else if (pageId === 'pronunciation') {
                initPronunciation();
            } else if (pageId === 'guess-who') {
                initGuessWho();
            } else if (pageId === 'tic-tac-toe') {
                initTicTacToe();
            } else if (pageId === 'jeopardy') {
                initJeopardy();
            } else if (pageId === 'family-feud') {
                initFamilyFeud();
            }
        }

        // Detective Puzzles
        function showAnswer(button, answer) {
            const answerDiv = button.nextElementSibling;
            answerDiv.textContent = answer;
            answerDiv.style.display = 'block';
            button.style.display = 'none';
        }

        // Memory Game
        const vocabularyPairs = [
            ['waiter', 'retaiw'],
            ['menu', 'unem'],
            ['customer', 'remotsuc'],
            ['uniform', 'mrofinu'],
            ['bottle', 'elttob'],
            ['pasta', 'atsap'],
            ['pizza', 'azzip'],
            ['restaurant', 'tnaruatser']
        ];

        function initMemoryGame() {
            resetMemoryGame();
        }

        function resetMemoryGame() {
            const grid = document.getElementById('memory-grid');
            grid.innerHTML = '';
            memoryCards = [];
            flippedCards = [];
            matchedPairs = 0;
            
            // Create cards array
            const allCards = [];
            vocabularyPairs.forEach(pair => {
                allCards.push({type: 'correct', word: pair[0]});
                allCards.push({type: 'scrambled', word: pair[1]});
            });

            // Shuffle cards
            for (let i = allCards.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [allCards[i], allCards[j]] = [allCards[j], allCards[i]];
            }

            // Create card elements
            allCards.forEach((card, index) => {
                const cardElement = document.createElement('button');
                cardElement.className = 'memory-cell';
                cardElement.textContent = index + 1; // Show numbers 1-16
                cardElement.onclick = () => flipCard(index);
                cardElement.dataset.word = card.word;
                cardElement.dataset.type = card.type;
                grid.appendChild(cardElement);
                memoryCards.push(cardElement);
            });

            updateMemoryScore();
        }

        function shuffleMemoryGame() {
            resetMemoryGame();
        }

        function flipCard(index) {
            const card = memoryCards[index];
            
            if (card.classList.contains('flipped') || card.classList.contains('matched') || flippedCards.length >= 2) {
                return;
            }

            card.classList.add('flipped');
            card.textContent = card.dataset.word;
            flippedCards.push({card: card, index: index});

            if (flippedCards.length === 2) {
                setTimeout(checkMatch, 1000);
            }
        }

        function checkMatch() {
            const [card1, card2] = flippedCards;
            
            // Check if it's a matching pair
            const isMatch = vocabularyPairs.some(pair => 
                (card1.card.dataset.word === pair[0] && card2.card.dataset.word === pair[1]) ||
                (card1.card.dataset.word === pair[1] && card2.card.dataset.word === pair[0])
            );

            if (isMatch) {
                card1.card.classList.add('matched');
                card2.card.classList.add('matched');
                matchedPairs++;
                updateMemoryScore();
            } else {
                card1.card.classList.remove('flipped');
                card2.card.classList.remove('flipped');
                card1.card.textContent = card1.index + 1; // Show card number
                card2.card.textContent = card2.index + 1; // Show card number
            }

            flippedCards = [];
        }

        function updateMemoryScore() {
            document.getElementById('memory-score').textContent = `Matches: ${matchedPairs}/8`;
        }

        // Vocabulary Quiz
        const vocabQuestions = [
            {question: "Who brings food to your table?", options: ["waiter", "customer", "cook", "manager"], correct: "waiter"},
            {question: "What do you read to choose food?", options: ["book", "menu", "newspaper", "letter"], correct: "menu"},
            {question: "Who eats at the restaurant?", options: ["waiter", "customer", "cook", "cleaner"], correct: "customer"},
            {question: "What do waiters wear?", options: ["pajamas", "uniform", "swimsuit", "costume"], correct: "uniform"},
            {question: "What do you drink water from?", options: ["plate", "bowl", "bottle", "spoon"], correct: "bottle"},
            {question: "Long thin food from Italy?", options: ["pizza", "pasta", "rice", "bread"], correct: "pasta"},
            {question: "Round food with cheese and tomato?", options: ["pasta", "salad", "pizza", "soup"], correct: "pizza"}
        ];

        function initVocabQuiz() {
            const container = document.getElementById('vocab-questions');
            container.innerHTML = '';
            vocabQuizScore = 0;
            
            vocabQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'quiz-question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}: ${q.question}</h3>
                    <div class="quiz-options">
                        ${q.options.map(option => 
                            `<div class="quiz-option" onclick="selectVocabAnswer('${option}', '${q.correct}', this)">${option}</div>`
                        ).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });
            
            updateVocabQuizScore();
        }

        function selectVocabAnswer(selected, correct, element) {
            const options = element.parentElement.querySelectorAll('.quiz-option');
            options.forEach(opt => opt.style.pointerEvents = 'none');
            
            if (selected === correct) {
                element.classList.add('correct');
                vocabQuizScore++;
            } else {
                element.classList.add('incorrect');
                options.forEach(opt => {
                    if (opt.textContent === correct) {
                        opt.classList.add('correct');
                    }
                });
            }
            
            updateVocabQuizScore();
        }

        function updateVocabQuizScore() {
            document.getElementById('quiz-score').textContent = `Score: ${vocabQuizScore}/7`;
        }

        function resetVocabQuiz() {
            initVocabQuiz();
        }

        // Character Profiles for Speed Reading
        const characterProfiles = {
            shaggy: `Norville "Shaggy" Rogers is Scooby-Doo's best friend. He is an easygoing teenager and he likes eating. He also drives the Mystery Machine. He has messy brown hair and he always wears a green shirt and brown pants. His catchphrase is "Zoinks!" when he is surprised or scared.`,
            
            velma: `Velma Dinkley is the smartest member of Mystery Inc. Velma who solves the mystery. She loves computers and she is the brains of the Scooby gang. She wears orange clothing and thick glasses. She can't see very well without her glasses. Sometimes the phrases are "Jinkies" and "My glasses, I can't see without my glasses!"`,
            
            daphne: `Daphne Blake or "Danger-prone Daphne" comes from a rich family. She's very beautiful and polite. She likes fashion and going shopping. She also likes martial arts. She often gets into trouble and asks for help. She can't run very fast in high heels. She has red hair and she wears purple clothing.`,
            
            fred: `Fred Jones is the leader of the Mystery Inc. gang. He is handsome and friendly. He likes sports, especially football and he is very strong. He wears an orange shirt and blue jeans. He drives the Mystery Machine. He really likes inventions. He catches the bad guys and he sometimes says "Let's split up gang!"`
        };

        function selectCharacter(character) {
            document.querySelectorAll('.quiz-option').forEach(opt => opt.classList.remove('selected'));
            event.target.classList.add('selected');
            
            const text = characterProfiles[character];
            document.getElementById('reading-text').innerHTML = `<p>${text}</p>`;
            currentWordIndex = 0;
        }

        function updateReadingSpeed() {
            readingSpeed = parseInt(document.getElementById('reading-speed').value);
        }

        function startReading() {
            const textElement = document.getElementById('reading-text');
            const text = textElement.textContent;
            const words = text.split(' ');
            
            if (words.length === 0) return;
            
            pauseReading();
            
            const wordsPerMinute = readingSpeed;
            const intervalTime = 60000 / wordsPerMinute;
            
            readingInterval = setInterval(() => {
                if (currentWordIndex >= words.length) {
                    pauseReading();
                    return;
                }
                
                // Highlight current word
                const highlightedText = words.map((word, index) => {
                    if (index === currentWordIndex) {
                        return `<span style="background-color: #667eea; color: white; padding: 2px 4px; border-radius: 3px;">${word}</span>`;
                    }
                    return word;
                }).join(' ');
                
                textElement.innerHTML = highlightedText;
                currentWordIndex++;
            }, intervalTime);
        }

        function pauseReading() {
            if (readingInterval) {
                clearInterval(readingInterval);
                readingInterval = null;
            }
        }

        function resetReading() {
            pauseReading();
            currentWordIndex = 0;
            const textElement = document.getElementById('reading-text');
            if (textElement.textContent !== 'Select a character profile to begin speed reading.') {
                textElement.innerHTML = textElement.textContent;
            }
        }

        // Comprehension Quiz
        const comprehensionQuestions = [
            {question: "Who solves the mystery?", options: ["Shaggy", "Velma", "Daphne", "Fred"], correct: "Velma"},
            {question: "Who is brave but danger-prone?", options: ["Velma", "Shaggy", "Daphne", "Scooby"], correct: "Daphne"},
            {question: "Who is Shaggy's best friend?", options: ["Fred", "Velma", "Daphne", "Scooby-Doo"], correct: "Scooby-Doo"},
            {question: "Who drives the Mystery Machine?", options: ["Velma", "Daphne", "Norville", "Fred"], correct: "Norville"},
            {question: "Who wears glasses?", options: ["Fred", "Daphne", "Velma", "Shaggy"], correct: "Velma"},
            {question: "Who says 'Zoinks'?", options: ["Fred", "Velma", "Norville", "Daphne"], correct: "Norville"}
        ];

        function initComprehensionQuiz() {
            const container = document.getElementById('comprehension-questions');
            container.innerHTML = '';
            comprehensionScore = 0;
            
            comprehensionQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'quiz-question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}: ${q.question}</h3>
                    <div class="quiz-options">
                        ${q.options.map(option => 
                            `<div class="quiz-option" onclick="selectComprehensionAnswer('${option}', '${q.correct}', this)">${option}</div>`
                        ).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });
            
            updateComprehensionScore();
        }

        function selectComprehensionAnswer(selected, correct, element) {
            const options = element.parentElement.querySelectorAll('.quiz-option');
            options.forEach(opt => opt.style.pointerEvents = 'none');
            
            if (selected === correct) {
                element.classList.add('correct');
                comprehensionScore++;
            } else {
                element.classList.add('incorrect');
                options.forEach(opt => {
                    if (opt.textContent === correct) {
                        opt.classList.add('correct');
                    }
                });
            }
            
            updateComprehensionScore();
        }

        function updateComprehensionScore() {
            document.getElementById('comprehension-score').textContent = `Score: ${comprehensionScore}/6`;
        }

        function resetComprehensionQuiz() {
            initComprehensionQuiz();
        }

        // Gap Fill Exercise
        const gapFillWords = ['danger', 'friend', 'glasses', 'brave', 'solve', 'mystery', 'detective', 'clues'];
        const gapFillText = `Velma is a smart _____ who likes to _____ mysteries. She wears _____ and she can't see without them. She looks for _____ to solve each _____. Shaggy is Scooby's best _____ and he is not very _____. Sometimes they face _____ together.`;

        function initGapFill() {
            const wordBank = document.getElementById('word-bank');
            const textContainer = document.getElementById('gap-fill-text');
            
            wordBank.innerHTML = '';
            gapFillScore = 0;
            
            // Shuffle words
            const shuffledWords = [...gapFillWords].sort(() => Math.random() - 0.5);
            
            shuffledWords.forEach(word => {
                const wordElement = document.createElement('div');
                wordElement.className = 'quiz-option';
                wordElement.textContent = word;
                wordElement.onclick = () => selectGapFillWord(word, wordElement);
                wordBank.appendChild(wordElement);
            });
            
            // Create text with gaps
            const textWithGaps = gapFillText.replace(/___/g, '<span class="gap" onclick="clearGap(this)">___</span>');
            textContainer.innerHTML = `<div style="font-size: 1.2em; line-height: 2;">${textWithGaps}</div>`;
            
            updateGapFillScore();
        }

        function selectGapFillWord(word, element) {
            const gaps = document.querySelectorAll('.gap');
            const emptyGap = Array.from(gaps).find(gap => gap.textContent === '___');
            
            if (emptyGap) {
                emptyGap.textContent = word;
                emptyGap.style.backgroundColor = '#667eea';
                emptyGap.style.color = 'white';
                emptyGap.style.padding = '2px 8px';
                emptyGap.style.borderRadius = '4px';
                element.style.display = 'none';
                
                gapFillScore++;
                updateGapFillScore();
            }
        }

        function clearGap(gap) {
            if (gap.textContent !== '___') {
                const word = gap.textContent;
                gap.textContent = '___';
                gap.style.backgroundColor = '';
                gap.style.color = '';
                gap.style.padding = '';
                gap.style.borderRadius = '';
                
                // Show word back in bank
                const wordBank = document.getElementById('word-bank');
                const wordElements = wordBank.querySelectorAll('.quiz-option');
                wordElements.forEach(el => {
                    if (el.textContent === word) {
                        el.style.display = 'block';
                    }
                });
                
                gapFillScore--;
                updateGapFillScore();
            }
        }

        function updateGapFillScore() {
            document.getElementById('gap-fill-score').textContent = `Completed: ${gapFillScore}/8`;
        }

        function resetGapFill() {
            initGapFill();
        }

        // Reorder Text Exercise
        const correctOrder = [
            "Mystery Inc. is a group of teenage detectives.",
            "They drive around in the Mystery Machine.",
            "Velma is the smartest member of the group.",
            "She wears glasses and solves mysteries.",
            "Shaggy and Scooby are always hungry.",
            "They often get scared during investigations.",
            "Fred is the leader and he's very brave.",
            "Daphne is fashionable but danger-prone."
        ];

        function initReorderText() {
            const container = document.getElementById('reorder-container');
            container.innerHTML = '';
            
            const shuffled = [...correctOrder].sort(() => Math.random() - 0.5);
            
            shuffled.forEach((sentence, index) => {
                const sentenceElement = document.createElement('div');
                sentenceElement.className = 'card';
                sentenceElement.style.cursor = 'move';
                sentenceElement.style.margin = '10px 0';
                sentenceElement.textContent = sentence;
                sentenceElement.draggable = true;
                sentenceElement.dataset.sentence = sentence;
                
                sentenceElement.ondragstart = function(e) {
                    e.dataTransfer.setData('text/plain', sentence);
                    this.style.opacity = '0.5';
                };
                
                sentenceElement.ondragend = function(e) {
                    this.style.opacity = '1';
                };
                
                sentenceElement.ondragover = function(e) {
                    e.preventDefault();
                };
                
                sentenceElement.ondrop = function(e) {
                    e.preventDefault();
                    const draggedSentence = e.dataTransfer.getData('text/plain');
                    const draggedElement = Array.from(container.children).find(el => el.dataset.sentence === draggedSentence);
                    
                    const afterElement = getDragAfterElement(container, e.clientY);
                    if (afterElement == null) {
                        container.appendChild(draggedElement);
                    } else {
                        container.insertBefore(draggedElement, afterElement);
                    }
                };
                
                container.appendChild(sentenceElement);
            });
            
            document.getElementById('reorder-score').textContent = 'Status: Ready to Check';
        }

        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.card:not(.dragging)')];
            
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;
                
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        function shuffleReorderText() {
            initReorderText();
        }

        function checkReorderText() {
            const container = document.getElementById('reorder-container');
            const sentences = Array.from(container.children).map(el => el.textContent);
            
            let correct = 0;
            sentences.forEach((sentence, index) => {
                const element = container.children[index];
                if (sentence === correctOrder[index]) {
                    element.style.backgroundColor = '#c6f6d5';
                    element.style.borderColor = '#38a169';
                    correct++;
                } else {
                    element.style.backgroundColor = '#fed7d7';
                    element.style.borderColor = '#f56565';
                }
            });
            
            document.getElementById('reorder-score').textContent = `Correct: ${correct}/8`;
        }

        function resetReorderText() {
            initReorderText();
        }

        // Pronunciation Minefield Game
        const pronunciationWords = [
            'mystery', 'history', 'friend', 'brave', 'cave', 'face', 'safe', 'save',
            'solve', 'glove', 'love', 'move', 'danger', 'stranger', 'glasses', 'classes',
            'smart', 'heart', 'start', 'part', 'scared', 'shared', 'tired', 'fired',
            'clues', 'news', 'choose', 'lose', 'detective', 'active', 'character', 'chapter',
            'ghost', 'most', 'coast', 'toast'
        ];
        
        let teamLives = [3, 3, 3, 3];
        let currentTeam = 0;
        let bombPositions = [];

        function initPronunciation() {
            resetPronunciation();
        }

        function resetPronunciation() {
            teamLives = [3, 3, 3, 3];
            currentTeam = 0;
            bombPositions = [];
            
            // Generate random bomb positions (10 bombs out of 36 cells)
            const totalCells = 36;
            const bombCount = 10;
            const bombs = new Set();
            
            while (bombs.size < bombCount) {
                bombs.add(Math.floor(Math.random() * totalCells));
            }
            bombPositions = Array.from(bombs);
            
            updateTeamLives();
            
            const grid = document.getElementById('pronunciation-grid');
            grid.innerHTML = '';
            
            const rows = ['A', 'B', 'C', 'D', 'E', 'F'];
            
            rows.forEach((row, rowIndex) => {
                // Add row label
                const rowLabel = document.createElement('div');
                rowLabel.className = 'pronunciation-row-label';
                rowLabel.textContent = row;
                grid.appendChild(rowLabel);
                
                // Add cells for this row
                for (let col = 1; col <= 6; col++) {
                    const cellIndex = rowIndex * 6 + (col - 1);
                    const cell = document.createElement('button');
                    cell.className = 'pronunciation-cell';
                    const coordinate = `${row}${col}`;
                    const word = pronunciationWords[cellIndex];
                    cell.innerHTML = `<div class="coordinate-label">${coordinate}</div><div class="word-label">${word}</div>`;
                    cell.onclick = () => clickPronunciationCell(cellIndex, cell);
                    cell.dataset.word = word;
                    cell.dataset.coordinate = coordinate;
                    grid.appendChild(cell);
                }
            });
        }

        function clickPronunciationCell(index, cell) {
            if (cell.classList.contains('revealed')) return;
            
            cell.classList.add('revealed');
            // Words are always visible, just add visual feedback
            
            if (bombPositions.includes(index)) {
                cell.classList.add('bomb');
                teamLives[currentTeam]--;
                updateTeamLives();
                
                // Show bomb notification
                showGameNotification(`💣 BOOM! Team ${currentTeam + 1} hit a bomb!`, 'bomb');
                
                if (teamLives[currentTeam] <= 0) {
                    setTimeout(() => {
                        showGameNotification(`💀 Team ${currentTeam + 1} is eliminated!`, 'elimination');
                    }, 500);
                    
                    // Check if there's a winner
                    const aliveTeams = teamLives.filter(lives => lives > 0);
                    if (aliveTeams.length === 1) {
                        const winnerTeam = teamLives.findIndex(lives => lives > 0) + 1;
                        setTimeout(() => {
                            showGameNotification(`🏆 Team ${winnerTeam} WINS!`, 'winner', 3000);
                        }, 1000);
                        return; // Don't switch teams - game is over
                    }
                }
            } else {
                cell.classList.add('safe');
                showGameNotification(`✅ Safe! Team ${currentTeam + 1} survives`, 'default', 1500);
            }
            
            // Switch to next team (only if game isn't over)
            const aliveTeams = teamLives.filter(lives => lives > 0);
            if (aliveTeams.length > 1) {
                do {
                    currentTeam = (currentTeam + 1) % 4;
                } while (teamLives[currentTeam] <= 0 && teamLives.some(lives => lives > 0));
                
                document.getElementById('current-team').textContent = `Team ${currentTeam + 1}`;
            }
        }

        function updateTeamLives() {
            for (let i = 0; i < 4; i++) {
                const hearts = '❤️'.repeat(teamLives[i]) + '💔'.repeat(3 - teamLives[i]);
                document.getElementById(`team${i + 1}-lives`).textContent = hearts;
            }
        }

        // Guess Who Game
        const guessWhoCharacters = [
            {
                name: 'Velma',
                clue: 'She can\'t see without glasses',
                svg: `<svg width="150" height="150" viewBox="0 0 150 150">
                    <circle cx="75" cy="60" r="35" fill="#FDBCB4"/>
                    <rect x="60" y="45" width="30" height="20" rx="15" fill="#8B4513"/>
                    <circle cx="65" cy="52" r="8" fill="white" stroke="#333" stroke-width="2"/>
                    <circle cx="85" cy="52" r="8" fill="white" stroke="#333" stroke-width="2"/>
                    <circle cx="65" cy="52" r="3" fill="#333"/>
                    <circle cx="85" cy="52" r="3" fill="#333"/>
                    <path d="M70 65 Q75 70 80 65" stroke="#333" stroke-width="2" fill="none"/>
                    <rect x="50" y="95" width="50" height="40" fill="#FF8C00"/>
                    <rect x="45" y="130" width="60" height="20" fill="#8B4513"/>
                </svg>`
            },
            {
                name: 'Shaggy',
                clue: 'He says "Zoinks!" when scared',
                svg: `<svg width="150" height="150" viewBox="0 0 150 150">
                    <circle cx="75" cy="60" r="35" fill="#FDBCB4"/>
                    <path d="M55 35 Q65 25 75 35 Q85 25 95 35" fill="#8B4513"/>
                    <circle cx="65" cy="55" r="3" fill="#333"/>
                    <circle cx="85" cy="55" r="3" fill="#333"/>
                    <path d="M70 65 Q75 70 80 65" stroke="#333" stroke-width="2" fill="none"/>
                    <rect x="50" y="95" width="50" height="40" fill="#228B22"/>
                    <rect x="45" y="130" width="60" height="20" fill="#8B4513"/>
                    <circle cx="40" cy="70" r="8" fill="#FDBCB4"/>
                    <circle cx="110" cy="70" r="8" fill="#FDBCB4"/>
                </svg>`
            },
            {
                name: 'Daphne',
                clue: 'She has red hair and wears purple',
                svg: `<svg width="150" height="150" viewBox="0 0 150 150">
                    <circle cx="75" cy="60" r="35" fill="#FDBCB4"/>
                    <path d="M45 35 Q55 25 65 35 Q75 25 85 35 Q95 25 105 35" fill="#FF6347"/>
                    <circle cx="65" cy="55" r="3" fill="#333"/>
                    <circle cx="85" cy="55" r="3" fill="#333"/>
                    <path d="M70 65 Q75 70 80 65" stroke="#333" stroke-width="2" fill="none"/>
                    <rect x="50" y="95" width="50" height="40" fill="#9932CC"/>
                    <rect x="45" y="130" width="60" height="20" fill="#8B4513"/>
                </svg>`
            },
            {
                name: 'Fred',
                clue: 'He drives the Mystery Machine and wears orange',
                svg: `<svg width="150" height="150" viewBox="0 0 150 150">
                    <circle cx="75" cy="60" r="35" fill="#FDBCB4"/>
                    <path d="M50 35 Q60 30 70 35 Q80 30 90 35 Q100 30 100 35" fill="#FFD700"/>
                    <circle cx="65" cy="55" r="3" fill="#333"/>
                    <circle cx="85" cy="55" r="3" fill="#333"/>
                    <path d="M70 65 Q75 70 80 65" stroke="#333" stroke-width="2" fill="none"/>
                    <rect x="50" y="95" width="50" height="40" fill="#FF8C00"/>
                    <rect x="45" y="130" width="60" height="20" fill="#0000FF"/>
                </svg>`
            },
            {
                name: 'Scooby-Doo',
                clue: 'He is a dog who loves Scooby Snacks',
                svg: `<svg width="150" height="150" viewBox="0 0 150 150">
                    <ellipse cx="75" cy="70" rx="40" ry="35" fill="#8B4513"/>
                    <circle cx="60" cy="60" r="8" fill="white"/>
                    <circle cx="90" cy="60" r="8" fill="white"/>
                    <circle cx="60" cy="60" r="4" fill="#333"/>
                    <circle cx="90" cy="60" r="4" fill="#333"/>
                    <ellipse cx="75" cy="80" rx="15" ry="8" fill="#333"/>
                    <circle cx="75" cy="78" r="3" fill="white"/>
                    <path d="M45 45 Q50 35 55 45" fill="#8B4513"/>
                    <path d="M95 45 Q100 35 105 45" fill="#8B4513"/>
                    <rect x="60" y="105" width="30" height="15" fill="#0000FF"/>
                </svg>`
            }
        ];

        let guessWhoRound = 1;
        let guessWhoScore = 0;
        let currentCharacter = null;

        function initGuessWho() {
            guessWhoRound = 1;
            guessWhoScore = 0;
            updateGuessWhoDisplay();
            loadNewCharacter();
        }

        function loadNewCharacter() {
            currentCharacter = guessWhoCharacters[Math.floor(Math.random() * guessWhoCharacters.length)];
            document.getElementById('character-display').innerHTML = '';
            document.getElementById('character-clue').textContent = 'Click the card above to start';
            document.getElementById('reveal-answer').style.display = 'none';
            
            const card = document.getElementById('guess-who-card');
            card.onclick = showGuessWhoClue;
        }

        function showGuessWhoClue() {
            document.getElementById('character-display').innerHTML = currentCharacter.svg;
            document.getElementById('character-clue').innerHTML = `<strong>Clue:</strong> ${currentCharacter.clue}`;
            document.getElementById('reveal-answer').style.display = 'inline-block';
            document.getElementById('guess-who-card').onclick = null;
        }

        function revealGuessWhoAnswer() {
            document.getElementById('character-clue').innerHTML += `<br><strong>Answer:</strong> ${currentCharacter.name}`;
            document.getElementById('reveal-answer').style.display = 'none';
            guessWhoScore++;
            updateGuessWhoDisplay();
        }

        function nextGuessWhoRound() {
            if (guessWhoRound < 5) {
                guessWhoRound++;
                updateGuessWhoDisplay();
                loadNewCharacter();
            } else {
                alert(`Game Complete! Final Score: ${guessWhoScore}/5`);
            }
        }

        function resetGuessWho() {
            initGuessWho();
        }

        function updateGuessWhoDisplay() {
            document.getElementById('guess-who-round').textContent = guessWhoRound;
            document.getElementById('guess-who-score').textContent = guessWhoScore;
        }

        // Tic Tac Toe Game
        const ticTacToeWords = [
            {word: 'Velma', sign: '+', sentence: 'Velma wears glasses.'},
            {word: 'drive', sign: '-', sentence: 'Does Scooby drive? No, he doesn\'t.'},
            {word: 'mystery', sign: '+', sentence: 'They solve mysteries.'},
            {word: 'scared', sign: '?', sentence: 'Is Shaggy scared? Yes, he is.'},
            {word: 'smart', sign: '+', sentence: 'Velma is smart.'},
            {word: 'purple', sign: '+', sentence: 'Daphne wears purple.'},
            {word: 'cook', sign: '-', sentence: 'Fred doesn\'t cook.'},
            {word: 'glasses', sign: '?', sentence: 'Who wears glasses? Velma does.'},
            {word: 'brave', sign: '+', sentence: 'Fred is brave.'},
            {word: 'hungry', sign: '?', sentence: 'Are they hungry? Yes, they are.'},
            {word: 'ghost', sign: '-', sentence: 'Ghosts aren\'t real.'},
            {word: 'orange', sign: '+', sentence: 'Fred wears orange.'},
            {word: 'friend', sign: '+', sentence: 'Scooby is Shaggy\'s friend.'},
            {word: 'solve', sign: '?', sentence: 'Do they solve mysteries? Yes, they do.'},
            {word: 'dog', sign: '+', sentence: 'Scooby is a dog.'},
            {word: 'fly', sign: '-', sentence: 'Dogs don\'t fly.'},
            {word: 'red', sign: '+', sentence: 'Daphne has red hair.'},
            {word: 'danger', sign: '?', sentence: 'Is there danger? Sometimes there is.'},
            {word: 'green', sign: '+', sentence: 'Shaggy wears green.'},
            {word: 'monster', sign: '-', sentence: 'Monsters aren\'t real.'},
            {word: 'clues', sign: '+', sentence: 'They find clues.'},
            {word: 'machine', sign: '+', sentence: 'They drive the Mystery Machine.'},
            {word: 'detective', sign: '+', sentence: 'They are detectives.'},
            {word: 'swim', sign: '-', sentence: 'Scooby doesn\'t swim well.'},
            {word: 'team', sign: '+', sentence: 'They work as a team.'},
            {word: 'scared', sign: '?', sentence: 'Who gets scared? Shaggy and Scooby do.'},
            {word: 'food', sign: '+', sentence: 'Shaggy loves food.'},
            {word: 'villain', sign: '-', sentence: 'They catch villains.'},
            {word: 'help', sign: '+', sentence: 'They help each other.'},
            {word: 'snacks', sign: '+', sentence: 'Scooby loves snacks.'},
            {word: 'leader', sign: '+', sentence: 'Fred is the leader.'},
            {word: 'glasses', sign: '-', sentence: 'Shaggy doesn\'t wear glasses.'},
            {word: 'smart', sign: '?', sentence: 'Who is smart? Velma is.'},
            {word: 'van', sign: '+', sentence: 'They travel in a van.'},
            {word: 'afraid', sign: '-', sentence: 'Fred isn\'t afraid.'},
            {word: 'young', sign: '+', sentence: 'They are young detectives.'},
            {word: 'old', sign: '-', sentence: 'They aren\'t old.'},
            {word: 'mystery', sign: '?', sentence: 'What do they solve? Mysteries.'},
            {word: 'together', sign: '+', sentence: 'They work together.'},
            {word: 'alone', sign: '-', sentence: 'They don\'t work alone.'},
            {word: 'adventures', sign: '+', sentence: 'They have adventures.'},
            {word: 'blue', sign: '+', sentence: 'Fred wears blue jeans.'},
            {word: 'laugh', sign: '+', sentence: 'Shaggy makes people laugh.'},
            {word: 'serious', sign: '-', sentence: 'Shaggy isn\'t serious.'},
            {word: 'investigate', sign: '+', sentence: 'They investigate mysteries.'},
            {word: 'ignore', sign: '-', sentence: 'They don\'t ignore clues.'},
            {word: 'friends', sign: '?', sentence: 'Are they friends? Yes, they are.'},
            {word: 'enemies', sign: '-', sentence: 'They aren\'t enemies.'},
            {word: 'group', sign: '+', sentence: 'They are a group.'}
        ];

        let ticTacToeGrid = [];
        let currentPlayer = 'red';
        let redScore = 0;
        let blueScore = 0;

        function initTicTacToe() {
            resetTicTacToe();
        }

        function resetTicTacToe() {
            ticTacToeGrid = Array(7).fill().map(() => Array(7).fill(null));
            currentPlayer = 'red';
            redScore = 0;
            blueScore = 0;
            
            updateTicTacToeScore();
            
            const grid = document.getElementById('tic-tac-toe-grid');
            grid.innerHTML = '';
            
            const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
            
            rows.forEach((row, rowIndex) => {
                // Add row label
                const rowLabel = document.createElement('div');
                rowLabel.className = 'tic-tac-toe-row-label';
                rowLabel.textContent = row;
                grid.appendChild(rowLabel);
                
                // Add cells for this row
                for (let col = 1; col <= 7; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'tic-tac-toe-cell';
                    const wordData = ticTacToeWords[(rowIndex * 7) + (col - 1)];
                    cell.innerHTML = `<strong>${wordData.word}</strong><br><small>${wordData.sign}</small>`;
                    cell.onclick = () => claimTicTacToeCell(rowIndex, col - 1, cell, wordData);
                    grid.appendChild(cell);
                }
            });
        }

        function claimTicTacToeCell(row, col, cell, wordData) {
            if (ticTacToeGrid[row][col] !== null) return;
            
            // Teacher will listen to student and award square if correct
            ticTacToeGrid[row][col] = currentPlayer;
            cell.classList.add(currentPlayer);
            cell.onclick = null;
            
            // Check for 4 in a row
            if (checkTicTacToeWin(row, col)) {
                if (currentPlayer === 'red') {
                    redScore++;
                    alert('Red team scores!');
                } else {
                    blueScore++;
                    alert('Blue team scores!');
                }
                updateTicTacToeScore();
            }
            
            // Switch players
            currentPlayer = currentPlayer === 'red' ? 'blue' : 'red';
            document.getElementById('current-turn').textContent = currentPlayer === 'red' ? '🔴 Red' : '🔵 Blue';
        }

        function checkTicTacToeWin(row, col) {
            const directions = [
                [0, 1], [1, 0], [1, 1], [1, -1]
            ];
            
            for (let [dx, dy] of directions) {
                let count = 1;
                
                // Check in one direction
                for (let i = 1; i < 4; i++) {
                    const newRow = row + dx * i;
                    const newCol = col + dy * i;
                    if (newRow >= 0 && newRow < 7 && newCol >= 0 && newCol < 7 && 
                        ticTacToeGrid[newRow][newCol] === currentPlayer) {
                        count++;
                    } else {
                        break;
                    }
                }
                
                // Check in opposite direction
                for (let i = 1; i < 4; i++) {
                    const newRow = row - dx * i;
                    const newCol = col - dy * i;
                    if (newRow >= 0 && newRow < 7 && newCol >= 0 && newCol < 7 && 
                        ticTacToeGrid[newRow][newCol] === currentPlayer) {
                        count++;
                    } else {
                        break;
                    }
                }
                
                if (count >= 4) return true;
            }
            
            return false;
        }

        function updateTicTacToeScore() {
            document.getElementById('red-score').textContent = redScore;
            document.getElementById('blue-score').textContent = blueScore;
        }

        // Jeopardy Game
        const jeopardyQuestions = {
            'Characters': [
                {question: 'This boy often comes to the restaurant', answer: 'Who is Max?', points: 100},
                {question: 'This girl notices the waiters uniforms', answer: 'Who is Holly?', points: 200},
                {question: 'This person orders pasta and salad', answer: 'Who is Dad?', points: 300},
                {question: 'This person serves food to customers', answer: 'Who is the waiter?', points: 400},
                {question: 'These people eat at restaurants', answer: 'Who are customers?', points: 500}
            ],
            'Food': [
                {question: 'Long thin food from Italy', answer: 'What is pasta?', points: 100},
                {question: 'Round food with cheese and tomato', answer: 'What is pizza?', points: 200},
                {question: 'Green food that is healthy', answer: 'What is salad?', points: 300},
                {question: 'Cold sweet treat the family gets free', answer: 'What is ice cream?', points: 400},
                {question: 'Hot liquid food you eat with a spoon', answer: 'What is soup?', points: 500}
            ],
            'Story': [
                {question: 'The restaurant they thought they were in', answer: 'What is Ken\'s Café?', points: 100},
                {question: 'The real name of the restaurant', answer: 'What is The Pizza Place?', points: 200},
                {question: 'What the waiters usually wear', answer: 'What are blue uniforms?', points: 300},
                {question: 'What the waiters are wearing today', answer: 'What are green uniforms?', points: 400},
                {question: 'Why the restaurant is different', answer: 'What is it just opened today?', points: 500}
            ],
            'Grammar': [
                {question: 'We use this for things we do many times', answer: 'What is present simple?', points: 100},
                {question: 'We use this for things happening right now', answer: 'What is present continuous?', points: 200},
                {question: 'They _____ wear blue (many times)', answer: 'What is usually?', points: 300},
                {question: 'They _____ wearing green (right now)', answer: 'What is are?', points: 400},
                {question: 'We _____ have pasta (many times)', answer: 'What is usually?', points: 500}
            ]
        };

        let jeopardyScore = 0;
        let usedQuestions = new Set();

        function initJeopardy() {
            resetJeopardy();
        }

        function resetJeopardy() {
            jeopardyScore = 0;
            usedQuestions.clear();
            updateJeopardyScore();
            
            const board = document.getElementById('jeopardy-board');
            board.innerHTML = '';
            
            const categories = Object.keys(jeopardyQuestions);
            
            // Add category headers
            categories.forEach(category => {
                const header = document.createElement('div');
                header.className = 'jeopardy-category';
                header.textContent = category;
                board.appendChild(header);
            });
            
            // Add question cells
            for (let i = 0; i < 5; i++) {
                categories.forEach(category => {
                    const cell = document.createElement('button');
                    cell.className = 'jeopardy-cell';
                    const points = (i + 1) * 100;
                    cell.textContent = `$${points}`;
                    cell.onclick = () => showJeopardyQuestion(category, i, cell);
                    board.appendChild(cell);
                });
            }
        }

        function showJeopardyQuestion(category, index, cell) {
            if (cell.classList.contains('used')) return;
            
            const question = jeopardyQuestions[category][index];
            const userAnswer = prompt(`${category} for $${question.points}:\n\n${question.question}\n\nYour answer:`);
            
            if (userAnswer !== null) {
                alert(`Correct answer: ${question.answer}`);
                const isCorrect = confirm('Was the answer correct?');
                
                if (isCorrect) {
                    jeopardyScore += question.points;
                    updateJeopardyScore();
                }
                
                cell.classList.add('used');
                cell.textContent = 'USED';
            }
        }

        function updateJeopardyScore() {
            document.getElementById('jeopardy-score').textContent = jeopardyScore;
        }

        // Family Feud Game
        const familyFeudCategories = [
            {
                category: 'Things in a Restaurant',
                answers: ['Tables', 'Menu', 'Waiters', 'Food', 'Customers']
            },
            {
                category: 'Food You Can Order',
                answers: ['Pizza', 'Pasta', 'Salad', 'Soup', 'Ice cream']
            },
            {
                category: 'Things to Drink',
                answers: ['Water', 'Coffee', 'Milk', 'Juice', 'Tea']
            },
            {
                category: 'Restaurant People',
                answers: ['Waiter', 'Waitress', 'Customer', 'Chef', 'Manager']
            }
        ];

        let feudRound = 1;
        let feudRevealed = 0;

        function initFamilyFeud() {
            resetFamilyFeud();
        }

        function resetFamilyFeud() {
            feudRound = 1;
            feudRevealed = 0;
            updateFamilyFeudDisplay();
            loadFamilyFeudCategory();
        }

        function loadFamilyFeudCategory() {
            const category = familyFeudCategories[feudRound - 1];
            document.getElementById('feud-category').textContent = `Category: ${category.category}`;
            
            const board = document.getElementById('family-feud-board');
            board.innerHTML = '';
            
            category.answers.forEach((answer, index) => {
                const answerDiv = document.createElement('div');
                answerDiv.className = 'feud-answer';
                answerDiv.innerHTML = `
                    <span>${index + 1}. <span class="hidden-answer">???</span></span>
                    <span class="answer-text" style="display: none;">${answer}</span>
                `;
                answerDiv.onclick = () => revealFamilyFeudAnswer(answerDiv);
                board.appendChild(answerDiv);
            });
            
            feudRevealed = 0;
            updateFamilyFeudDisplay();
        }

        function revealFamilyFeudAnswer(answerDiv) {
            if (answerDiv.classList.contains('revealed')) return;
            
            answerDiv.classList.add('revealed');
            const hiddenText = answerDiv.querySelector('.hidden-answer');
            const answerText = answerDiv.querySelector('.answer-text');
            
            hiddenText.textContent = answerText.textContent;
            feudRevealed++;
            updateFamilyFeudDisplay();
        }

        function nextFeudRound() {
            if (feudRound < 4) {
                feudRound++;
                loadFamilyFeudCategory();
            } else {
                alert('All categories completed!');
            }
        }

        function updateFamilyFeudDisplay() {
            document.getElementById('feud-round').textContent = feudRound;
            document.getElementById('feud-revealed').textContent = feudRevealed;
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            showPage('main-menu');
        });
    </script>
</body>
</html> 